#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Copyright Risk Remediator - 代码合规修改器
自动修复代码中的版权风险问题的命令行工具

Author: AI Assistant
License: MIT
"""

import argparse
import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional

from fixer.report_parser import ReportParser
from fixer.config_loader import ConfigLoader
from fixer.font_replacer import FontReplacer
from fixer.cdn_cleaner import CDNCleaner
from fixer.font_file_cleaner import FontFileCleaner
from fixer.license_suggester import LicenseSuggester


class CopyrightRiskRemediator:
    """代码合规修改器主类"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化修改器
        
        Args:
            config_path: 配置文件路径，默认使用 assets/font_replace_map.json
        """
        self.config_path = config_path or "assets/font_replace_map.json"
        self.config_loader = ConfigLoader()
        self.report_parser = ReportParser()
        
        # 加载配置
        self.config = self.config_loader.load_config(self.config_path)
        
        # 初始化各个修复器
        self.font_replacer = FontReplacer(self.config.get('font_replacements', {}))
        self.cdn_cleaner = CDNCleaner(self.config.get('risky_cdn_patterns', []))
        self.font_file_cleaner = FontFileCleaner(self.config.get('risky_font_keywords', []))
        self.license_suggester = LicenseSuggester(self.config.get('license_alternatives', {}))
        
        self.results = {
            'font_replacements': [],
            'cdn_cleanups': [],
            'font_file_cleanups': [],
            'license_suggestions': [],
            'errors': []
        }
    
    def run_remediation(self, report_path: str, target_dir: str = ".", 
                       dry_run: bool = False, backup: bool = False,
                       fix_types: List[str] = None) -> Dict:
        """运行修复流程
        
        Args:
            report_path: 审查报告文件路径
            target_dir: 目标代码目录
            dry_run: 是否仅预览不实际修改
            backup: 是否备份原文件
            fix_types: 指定修复类型列表，如 ['fonts', 'cdn', 'files', 'licenses']
        
        Returns:
            修复结果字典
        """
        try:
            # 解析审查报告
            print(f"正在解析审查报告: {report_path}")
            issues = self.report_parser.parse_report(report_path)
            
            if not issues:
                print("未在报告中发现需要修复的问题")
                return self.results
            
            print(f"发现 {len(issues)} 个问题需要修复")
            
            # 根据指定的修复类型执行修复
            fix_types = fix_types or ['fonts', 'cdn', 'files', 'licenses']
            
            if 'fonts' in fix_types:
                self._fix_fonts(issues, target_dir, dry_run, backup)
            
            if 'cdn' in fix_types:
                self._fix_cdn(issues, target_dir, dry_run, backup)
            
            if 'files' in fix_types:
                self._fix_font_files(issues, target_dir, dry_run, backup)
            
            if 'licenses' in fix_types:
                self._suggest_licenses(issues)
            
            return self.results
            
        except Exception as e:
            error_msg = f"修复过程中发生错误: {str(e)}"
            print(f"错误: {error_msg}")
            self.results['errors'].append(error_msg)
            return self.results
    
    def _fix_fonts(self, issues: List[Dict], target_dir: str, dry_run: bool, backup: bool):
        """修复字体问题"""
        font_issues = [issue for issue in issues if issue.get('type') == 'commercial_font']
        if not font_issues:
            return
        
        print(f"正在修复 {len(font_issues)} 个字体问题...")
        results = self.font_replacer.replace_fonts(target_dir, dry_run, backup)
        self.results['font_replacements'].extend(results)
    
    def _fix_cdn(self, issues: List[Dict], target_dir: str, dry_run: bool, backup: bool):
        """修复CDN问题"""
        cdn_issues = [issue for issue in issues if issue.get('type') == 'risky_cdn']
        if not cdn_issues:
            return
        
        print(f"正在修复 {len(cdn_issues)} 个CDN问题...")
        results = self.cdn_cleaner.clean_cdn_links(target_dir, dry_run, backup)
        self.results['cdn_cleanups'].extend(results)
    
    def _fix_font_files(self, issues: List[Dict], target_dir: str, dry_run: bool, backup: bool):
        """修复字体文件问题"""
        file_issues = [issue for issue in issues if issue.get('type') == 'font_file_risk']
        if not file_issues:
            return
        
        print(f"正在修复 {len(file_issues)} 个字体文件问题...")
        results = self.font_file_cleaner.clean_font_files(target_dir, dry_run, backup)
        self.results['font_file_cleanups'].extend(results)
    
    def _suggest_licenses(self, issues: List[Dict]):
        """提供许可证替换建议"""
        license_issues = [issue for issue in issues if issue.get('type') == 'license_risk']
        if not license_issues:
            return
        
        print(f"正在分析 {len(license_issues)} 个许可证问题...")
        suggestions = self.license_suggester.suggest_alternatives(license_issues)
        self.results['license_suggestions'].extend(suggestions)
    
    def generate_report(self, output_path: str, format_type: str = 'json'):
        """生成修复报告
        
        Args:
            output_path: 输出文件路径
            format_type: 报告格式，'json' 或 'markdown'
        """
        if format_type.lower() == 'json':
            self._generate_json_report(output_path)
        elif format_type.lower() == 'markdown':
            self._generate_markdown_report(output_path)
        else:
            raise ValueError(f"不支持的报告格式: {format_type}")
    
    def _generate_json_report(self, output_path: str):
        """生成JSON格式报告"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        print(f"JSON报告已生成: {output_path}")
    
    def _generate_markdown_report(self, output_path: str):
        """生成Markdown格式报告"""
        content = self._format_markdown_report()
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Markdown报告已生成: {output_path}")
    
    def _format_markdown_report(self) -> str:
        """格式化Markdown报告内容"""
        lines = [
            "# 代码合规修复报告",
            "",
            f"生成时间: {self._get_current_time()}",
            "",
        ]
        
        # 字体替换部分
        if self.results['font_replacements']:
            lines.extend([
                "## 字体替换",
                "",
                "| 文件路径 | 原字体 | 替换字体 | 状态 |",
                "|---------|--------|----------|------|"
            ])
            for item in self.results['font_replacements']:
                lines.append(f"| {item.get('file', '')} | {item.get('old_font', '')} | {item.get('new_font', '')} | {item.get('status', '')} |")
            lines.append("")
        
        # CDN清理部分
        if self.results['cdn_cleanups']:
            lines.extend([
                "## CDN链接清理",
                "",
                "| 文件路径 | 原链接 | 替换链接 | 状态 |",
                "|---------|--------|----------|------|"
            ])
            for item in self.results['cdn_cleanups']:
                lines.append(f"| {item.get('file', '')} | {item.get('old_url', '')} | {item.get('new_url', '')} | {item.get('status', '')} |")
            lines.append("")
        
        # 字体文件清理部分
        if self.results['font_file_cleanups']:
            lines.extend([
                "## 字体文件清理",
                "",
                "| 文件路径 | 操作 | 状态 |",
                "|---------|------|------|"
            ])
            for item in self.results['font_file_cleanups']:
                lines.append(f"| {item.get('file', '')} | {item.get('action', '')} | {item.get('status', '')} |")
            lines.append("")
        
        # 许可证建议部分
        if self.results['license_suggestions']:
            lines.extend([
                "## 许可证替换建议",
                "",
                "| 依赖库 | 当前许可证 | 建议替换 | 说明 |",
                "|-------|-----------|----------|------|"
            ])
            for item in self.results['license_suggestions']:
                lines.append(f"| {item.get('library', '')} | {item.get('current_license', '')} | {item.get('suggested_alternative', '')} | {item.get('reason', '')} |")
            lines.append("")
        
        # 错误信息部分
        if self.results['errors']:
            lines.extend([
                "## 错误信息",
                ""
            ])
            for error in self.results['errors']:
                lines.append(f"- {error}")
            lines.append("")
        
        return "\n".join(lines)
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="代码合规修改器 - 自动修复代码中的版权风险问题",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py --report ./audit.txt --fix-all --backup --config ./assets/font_replace_map.json
  python main.py --dry-run --fix-fonts-only
  python main.py --report ./audit.txt --target-dir ./src --output-report ./fix_report.json
        """
    )
    
    # 基本参数
    parser.add_argument('--report', '-r', type=str, 
                       help='审查报告文件路径')
    parser.add_argument('--target-dir', '-t', type=str, default='.',
                       help='目标代码目录 (默认: 当前目录)')
    parser.add_argument('--config', '-c', type=str,
                       help='配置文件路径 (默认: assets/font_replace_map.json)')
    
    # 修复选项
    parser.add_argument('--fix-all', action='store_true',
                       help='修复所有类型的问题')
    parser.add_argument('--fix-fonts-only', action='store_true',
                       help='仅修复字体问题')
    parser.add_argument('--fix-cdn-only', action='store_true',
                       help='仅修复CDN问题')
    parser.add_argument('--fix-files-only', action='store_true',
                       help='仅清理字体文件')
    parser.add_argument('--fix-licenses-only', action='store_true',
                       help='仅提供许可证建议')
    
    # 运行选项
    parser.add_argument('--dry-run', action='store_true',
                       help='仅预览修复计划，不实际修改文件')
    parser.add_argument('--backup', action='store_true',
                       help='修改前备份原文件 (.bak)')
    
    # 输出选项
    parser.add_argument('--output-report', '-o', type=str,
                       help='输出修复报告文件路径')
    parser.add_argument('--report-format', choices=['json', 'markdown'], default='json',
                       help='报告格式 (默认: json)')
    
    args = parser.parse_args()
    
    # 参数验证
    if not args.report and not args.dry_run:
        parser.error("必须指定 --report 参数或使用 --dry-run 模式")
    
    # 确定修复类型
    fix_types = []
    if args.fix_all:
        fix_types = ['fonts', 'cdn', 'files', 'licenses']
    else:
        if args.fix_fonts_only:
            fix_types.append('fonts')
        if args.fix_cdn_only:
            fix_types.append('cdn')
        if args.fix_files_only:
            fix_types.append('files')
        if args.fix_licenses_only:
            fix_types.append('licenses')
        
        # 如果没有指定任何修复类型，默认修复所有
        if not fix_types:
            fix_types = ['fonts', 'cdn', 'files', 'licenses']
    
    try:
        # 创建修改器实例
        remediator = CopyrightRiskRemediator(args.config)
        
        # 运行修复
        if args.report:
            results = remediator.run_remediation(
                report_path=args.report,
                target_dir=args.target_dir,
                dry_run=args.dry_run,
                backup=args.backup,
                fix_types=fix_types
            )
        else:
            # dry-run 模式下创建示例结果
            print("Dry-run 模式: 预览修复计划...")
            results = remediator.results
        
        # 输出结果摘要
        print("\n修复完成!")
        print(f"字体替换: {len(results['font_replacements'])} 项")
        print(f"CDN清理: {len(results['cdn_cleanups'])} 项")
        print(f"字体文件清理: {len(results['font_file_cleanups'])} 项")
        print(f"许可证建议: {len(results['license_suggestions'])} 项")
        if results['errors']:
            print(f"错误: {len(results['errors'])} 项")
        
        # 生成报告
        if args.output_report:
            remediator.generate_report(args.output_report, args.report_format)
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
